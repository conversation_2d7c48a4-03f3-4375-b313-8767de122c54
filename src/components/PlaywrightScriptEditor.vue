<template>
  <div :class="['playwright-script-editor', { fullscreen: is_fullscreen }]">
    <div class="editor-header">
      <span class="editor-title">Playwright 脚本编辑器</span>
      <div class="editor-actions">
        <el-button size="small" type="info" @click="insertTemplate">插入模板</el-button>
        <el-tooltip :content="is_fullscreen ? '还原' : '最大化'">
          <el-button size="small" type="primary" circle @click="toggleFullscreen" style="margin-left: 8px;">
            <el-icon v-if="!is_fullscreen"><svg viewBox="0 0 1024 1024" width="16" height="16"><path d="M160 160h256v64H224v192h-64V160zm704 0v256h-64V224H608v-64h256zm-64 704H608v-64h192V608h64v256zm-576 0V608h64v192h192v64H160z"/></svg></el-icon>
            <el-icon v-else><svg viewBox="0 0 1024 1024" width="16" height="16"><path d="M224 224h192v64H288v128h-64V224zm576 0v192h-64V288H608v-64h192zm-64 576H608v-64h128V608h64v192zm-448 0V608h64v128h128v64H224z"/></svg></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>
    <code-mirror v-model="code_val" wrap basic :lang="lang" :style="is_fullscreen ? 'flex:1;height:calc(100vh - 48px);' : 'height: 400px;'" :extensions="extensions" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, toRaw } from 'vue';
import CodeMirror from 'vue-codemirror6'
import { oneDark } from '@codemirror/theme-one-dark'
import { python } from '@codemirror/lang-python';
import { indentUnit } from '@codemirror/language'

const emit = defineEmits(['updateScriptData'])

const props = defineProps({
  scriptData: String
})

// Playwright + pytest 模板
const playwright_template = `import pytest
from playwright.sync_api import Page, expect

def test_query_functionality(page: Page):
    """
    使用 Playwright 编写的查询功能测试脚本
    """
    # 访问登录页面
    page.goto("https://www.baidu.com/")

    # 填写搜索内容
    page.locator("#kw").fill("今天天气")

    # 验证搜索结果
    expect(page.get_by_role("link", name="百度首页", exact=True)).to_be_visible()
`

// 初始化代码值
const code_val = ref(playwright_template);

const lang = python();
const extensions = [oneDark, indentUnit.of("    ")];

// 全屏状态
const is_fullscreen = ref(false)
const toggleFullscreen = () => { is_fullscreen.value = !is_fullscreen.value }

// 插入模板
const insertTemplate = () => {
  code_val.value = playwright_template
  // 立即触发更新，确保父组件收到新内容
  updateData()
}

// 使用toRaw函数，这个函数可以返回响应式对象的原始非响应式值
const updateData = () => {
  emit('updateScriptData', toRaw(code_val.value))
}

// 监听代码变化，发送给父组件
watch(code_val, () => {
  updateData()
}, { deep: true })

// 监听从父组件传入的数据
watch(() => props.scriptData, (newValue) => {
  // 只有当传入的值与当前值不同时才更新，避免循环更新
  if (newValue !== code_val.value) {
    if (newValue && newValue.trim() !== '') {
      code_val.value = newValue
    } else if (!code_val.value || code_val.value.trim() === '') {
      // 只有当前编辑器也为空时，才设置为模板
      code_val.value = playwright_template
    }
  }
}, { immediate: true })
</script>

<style scoped>
.playwright-script-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  width: 100%;
  background: #fff;
  position: relative;
  transition: all 0.3s;
}
.playwright-script-editor.fullscreen {
  position: fixed !important;
  top: 0;
  left: 0;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999;
  border-radius: 0;
  box-shadow: 0 0 0 9999px rgba(0,0,0,0.2);
  background: #fff;
  display: flex;
  flex-direction: column;
}
.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
}
.editor-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}
.editor-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}
/* required! */
:deep(.cm-editor) {
  height: 100%;
  width: 100%;
}

:deep(.cm-focused) {
  outline: none;
}

:deep(.cm-scroller) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}
</style>